import { ChartInfo } from '@core/common/domain';
import Dashboard from '@/screens/dashboard-detail/components/dashboard/Dashboard';

export class PreviewChartSizeCalculator {
  private static readonly CELL_WIDTH = 27.5;
  private readonly chart?: ChartInfo | null;

  constructor(chart?: ChartInfo | null) {
    this.chart = chart;
  }

  calculate(): { height: string; width: string } {
    return {
      height: `${(this.chart?.getDefaultPosition().height ?? 1) * Dashboard.getCellHeight() - 16}px`,
      width: `${PreviewChartSizeCalculator.CELL_WIDTH * this.defaultChartWidth - 16}px`
    };
  }

  protected get defaultChartWidth() {
    return this.chart?.getDefaultPosition().width ?? 1;
  }
}
