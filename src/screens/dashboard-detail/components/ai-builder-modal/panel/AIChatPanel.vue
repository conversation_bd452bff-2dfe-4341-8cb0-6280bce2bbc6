<script lang="ts">
import { Component, Inject, Prop, Vue } from 'vue-property-decorator';
import LLMPanel from '@/screens/chart-builder/llm-panel/LLMPanel.vue';
import AIChatPanelHeader from '@/screens/dashboard-detail/components/ai-builder-modal/panel/AIChatPanelHeader.vue';
import AIChatInput from '@/screens/dashboard-detail/components/ai-builder-modal/panel/AIChatInput.vue';
import AIConversation from '@/screens/dashboard-detail/components/ai-builder-modal/panel/AIConversation.vue';
import { ChatMessageData } from '@/screens/dashboard-detail/intefaces/chatbot/ChatMessageData';
import { AIBuilderController } from '@/screens/dashboard-detail/components/ai-builder-modal/AIBuilderController';
import { MessageAction } from '@/screens/dashboard-detail/intefaces/chatbot/MessageAction';
import EventBus from '@/screens/dashboard-detail/components/chatbot/helpers/EventBus';
import { AIChatModalEvents } from '@/screens/dashboard-detail/components/ai-builder-modal/AIChatModalEvents';

@Component({
  components: { AIConversation, AIChatInput, AIChatPanelHeader, LLMPanel }
})
export default class AIChatPanel extends Vue {
  @Prop({ required: true })
  protected controller!: AIBuilderController;

  protected get conversations(): ChatMessageData[] {
    return this.controller?.conversations ?? [];
  }

  protected get typing(): boolean {
    return this.controller?.typing ?? false;
  }

  protected handleMsgSend(msg: MessageAction) {
    if (!this.controller) {
      return;
    }

    EventBus.$emit(AIChatModalEvents.sendMessage, msg);
  }
}
</script>

<template>
  <div class="ai-chat-panel">
    <AIChatPanelHeader id="ai-chat-panel-header" />
    <div id="ai-conversation">
      <AIConversation :mainData="conversations" :botTyping="typing" />
    </div>
    <AIChatInput id="ai-chat-input" input-placeholder="Hỏi bất kì điều gì" @msg-send="handleMsgSend" />
  </div>
</template>
<style lang="scss" src="@/screens/dashboard-detail/components/chatbot/chatbot.scss" />

<style lang="scss">
.ai-chat-panel {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  height: 100%;
  border-radius: 24px 0;

  #ai-conversation {
    flex: 1;
    position: relative;

    .di-board-content {
      position: absolute;
      height: 100%;
      width: 100%;

      text-align: start;
      word-break: break-word;
      white-space: pre-line;
    }
  }
}
</style>
