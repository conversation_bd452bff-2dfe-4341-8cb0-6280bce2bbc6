<script lang="ts">
import { Vue, Component } from 'vue-property-decorator';

@Component({})
export default class IconSidebar extends Vue {}
</script>

<template>
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M6.83496 4C6.38353 4.00419 6.01421 4.01228 5.69824 4.03809C5.31232 4.06962 5.03904 4.12274 4.82227 4.2002L4.62207 4.28614C4.18264 4.51004 3.81498 4.85043 3.55859 5.26856L3.45605 5.45215C3.33013 5.6993 3.25006 6.01362 3.20801 6.52832C3.16533 7.05073 3.16504 7.71893 3.16504 8.66309V11.3272C3.16504 12.2713 3.16533 12.9395 3.20801 13.4619C3.25006 13.9767 3.33013 14.291 3.45605 14.5381L3.55859 14.7217C3.81498 15.1398 4.18266 15.4802 4.62207 15.7041L4.82227 15.7901C5.03904 15.8675 5.31234 15.9206 5.69824 15.9522C6.01398 15.978 6.383 15.9861 6.83398 15.9903L6.83496 4ZM18.165 11.3272C18.165 12.2494 18.1653 12.9812 18.1172 13.5703C18.0745 14.0925 17.9916 14.5473 17.8125 14.9649L17.7295 15.1416C17.394 15.8001 16.8834 16.3512 16.2568 16.7354L15.9814 16.8897C15.5157 17.1269 15.0069 17.2286 14.4102 17.2774C13.821 17.3255 13.0893 17.3252 12.167 17.3252H7.83301C6.91071 17.3252 6.17898 17.3255 5.58984 17.2774C5.06757 17.2347 4.61294 17.1509 4.19531 16.9717L4.01855 16.8897C3.36014 16.5542 2.80898 16.0435 2.4248 15.417L2.27051 15.1416C2.03328 14.6759 1.93158 14.1671 1.88281 13.5703C1.83468 12.9812 1.83496 12.2494 1.83496 11.3272V8.66309C1.83496 7.7408 1.83468 7.00906 1.88281 6.41993C1.93157 5.82317 2.03329 5.3144 2.27051 4.84864L2.4248 4.57325C2.80898 3.94674 3.36012 3.43608 4.01855 3.10059L4.19531 3.01758C4.61285 2.83851 5.06771 2.75556 5.58984 2.71289C6.17898 2.66476 6.91071 2.66504 7.83301 2.66504H12.167C13.0893 2.66504 13.821 2.66476 14.4102 2.71289C15.0069 2.76165 15.5157 2.86337 15.9814 3.10059L16.2568 3.25489C16.8833 3.63906 17.394 4.1902 17.7295 4.84864L17.8125 5.02539C17.9916 5.44293 18.0745 5.89779 18.1172 6.41993C18.1653 7.00906 18.165 7.7408 18.165 8.66309V11.3272ZM8.16406 15.9951H12.167C13.1112 15.9951 13.7794 15.9948 14.3018 15.9522C14.8164 15.9101 15.1308 15.83 15.3779 15.7041L15.5615 15.6016C15.9797 15.3452 16.32 14.9775 16.5439 14.5381L16.6299 14.3379C16.7074 14.1211 16.7605 13.8479 16.792 13.4619C16.8347 12.9395 16.835 12.2713 16.835 11.3272V8.66309C16.835 7.71893 16.8347 7.05073 16.792 6.52832C16.7605 6.1424 16.7073 5.86912 16.6299 5.65235L16.5439 5.45215C16.32 5.01272 15.9796 4.64506 15.5615 4.38868L15.3779 4.28614C15.1308 4.16021 14.8165 4.08014 14.3018 4.03809C13.7794 3.99541 13.1112 3.99512 12.167 3.99512H8.16406L8.16504 4L8.16406 15.9951Z"
      fill="black"
    />
  </svg>
</template>

<style scoped lang="scss"></style>
