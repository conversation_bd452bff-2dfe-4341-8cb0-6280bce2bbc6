<script lang="ts">
import { Vue, Component, Inject, Prop } from 'vue-property-decorator';
import { ChartInfo } from '@core/common/domain';
import LoadingComponent from '@/shared/components/LoadingComponent.vue';
import ChartHolder from '@/screens/dashboard-detail/components/widget-container/charts/ChartHolder.vue';
import { PreviewChartSizeCalculator } from '@/screens/chart-builder/viz-panel/PreviewChartSizeCalculator';
import { AIBuilderController } from '@/screens/dashboard-detail/components/ai-builder-modal/AIBuilderController';

@Component({
  components: { ChartHolder, LoadingComponent }
})
export default class AIChartPreviewBody extends Vue {
  @Prop({ required: true })
  protected controller!: AIBuilderController;

  protected get chartInfo(): ChartInfo | null | undefined {
    return this.controller?.chartInfo;
  }

  protected get chartSize() {
    return new PreviewChartSizeCalculator(this.chartInfo).calculate();
  }
}
</script>

<template>
  <div class="ai-chart-preview-body">
    <ChartHolder
      ref="chartHolder"
      v-if="chartInfo"
      :key="chartInfo.id"
      disableSort
      :isPreview="true"
      auto-render-chart
      :meta-data="chartInfo"
      :style="chartSize"
    />
  </div>
</template>

<style scoped lang="scss">
.ai-chart-preview-body {
  flex: 1;
  position: relative;
  padding: 24px;
  border-radius: 0 0 24px 0;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
