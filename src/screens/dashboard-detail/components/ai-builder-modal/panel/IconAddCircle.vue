<script lang="ts">
import { Vue, Component } from 'vue-property-decorator';

@Component({})
export default class IconAddCircle extends Vue {}
</script>

<template>
  <svg width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M21 11C21 5.47715 16.5228 1 11 1C5.47715 1 1 5.47715 1 11C1 16.5228 5.47715 21 11 21C16.5228 21 21 16.5228 21 11Z"
      stroke="#141718"
      stroke-width="1.5"
    />
    <path d="M11 7V15M15 11L7 11" stroke="#141718" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
  </svg>
</template>

<style scoped lang="scss"></style>
