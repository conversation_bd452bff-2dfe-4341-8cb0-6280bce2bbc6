import { ChartOption, DIException, QuerySetting } from '@core/common/domain';
import { JsonUtils, Log } from '@core/utils';
import { ChartType, FunctionData } from '@/shared';
import { DraggableSettingResolver } from '@/shared/resolver';
import { _ConfigBuilderStore } from '@/screens/chart-builder/config-builder/ConfigBuilderStore';
import EventBus from '@/screens/dashboard-detail/components/chatbot/helpers/EventBus';
import { AIChatModalEvents } from '@/screens/dashboard-detail/components/ai-builder-modal/AIChatModalEvents';

export interface ChartResponse {
  querySetting: any;
  chartType: ChartType;
}

export interface ParsedMessage {
  text: string;
  codeBlock: string | null;
}

export class ChartMessageHelper {
  constructor(private factory: DraggableSettingResolver) {}

  /**
   * Parse response text to extract text and code block
   */
  parseResponse(response: string): ParsedMessage {
    const codeBlockRegex = /```([\s\S]*?)```/;
    const match = response.match(codeBlockRegex);

    const text = response.replace(/```[\s\S]*?```/g, '').trim();
    const codeBlock = match ? match[1].trim() : null;

    return { text, codeBlock };
  }

  /**
   * Extract chart response from code block
   */
  getChartResponse(codeBlock: string | null): ChartResponse | null {
    if (!codeBlock) {
      return null;
    }
    try {
      return JsonUtils.fromObject<ChartResponse>(codeBlock);
    } catch (ex) {
      Log.error('ChartMessageHelper::getChartResponse::error::', ex);
      return null;
    }
  }

  /**
   * Convert chart response to QuerySetting
   */
  toQuerySetting(chartResponse: ChartResponse): QuerySetting | null {
    if (!chartResponse) {
      return null;
    }

    try {
      const options = ChartOption.getDefaultChartOption(chartResponse.chartType);
      const querySetting: QuerySetting = this.convertToQuerySetting(chartResponse.querySetting);
      querySetting.setChartOption(options);
      return querySetting;
    } catch (ex) {
      Log.error('ChartMessageHelper::toQuerySetting::error::', ex);
      return null;
    }
  }

  /**
   * Check if message contains valid chart data
   */
  hasValidChart(messageText: string): boolean {
    const parsed = this.parseResponse(messageText);
    const chartResponse = this.getChartResponse(parsed.codeBlock);
    return !!(chartResponse && chartResponse.chartType && chartResponse.querySetting);
  }

  /**
   * Process chart message and emit configuration changes
   */
  processChartMessage(messageText: string): void {
    const parsed = this.parseResponse(messageText);
    const chartResponse = this.getChartResponse(parsed.codeBlock);

    if (!chartResponse || !chartResponse.chartType || !chartResponse.querySetting) {
      return;
    }

    const querySetting = this.toQuerySetting(chartResponse);
    this.emitDraggableSetting(chartResponse.chartType, querySetting);
  }

  /**
   * Emit draggable setting configuration
   */
  private emitDraggableSetting(chartType: ChartType, querySetting: QuerySetting | null): void {
    if (!querySetting) {
      EventBus.$emit(AIChatModalEvents.sendErrorMessage, 'Invalid query setting');
      return;
    }

    try {
      const { configsAsMap, filterAsMap } = this.factory.toDraggableSetting(chartType, querySetting);
      _ConfigBuilderStore.setSelectedChartType(chartType);
      _ConfigBuilderStore.setChartOption(querySetting.getChartOption()!);
      const configsRecord: Record<string, FunctionData[]> = {};
      configsAsMap.forEach((value, key) => {
        configsRecord[key] = value;
      });
      _ConfigBuilderStore.setConfigs(configsRecord);
      _ConfigBuilderStore.setFilter(filterAsMap);
      EventBus.$emit(AIChatModalEvents.onConfigChanged);
    } catch (ex) {
      Log.error('ChartMessageHelper::emitDraggableSetting::error::', ex);
      EventBus.$emit(AIChatModalEvents.sendErrorMessage, 'Failed to process chart configuration');
    }
  }

  /**
   * Convert query setting object to QuerySetting instance
   */
  private convertToQuerySetting(querySettingObj: any): QuerySetting {
    const result = QuerySetting.fromObject(querySettingObj);
    if (!result) {
      throw new DIException('Invalid query setting');
    }
    return result;
  }
}
