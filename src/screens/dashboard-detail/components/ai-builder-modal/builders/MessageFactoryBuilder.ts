import { MessageFactory } from '@/screens/dashboard-detail/components/ai-builder-modal/factories/MessageFactory';
import { MessageHandler } from '@/screens/dashboard-detail/components/ai-builder-modal/handlers/MessageHandler';
import { ChartHandler } from '@/screens/dashboard-detail/components/ai-builder-modal/handlers/ChartHandler';
import { ErrorHandler } from '@/screens/dashboard-detail/components/ai-builder-modal/handlers/ErrorHandler';
import { TextHandler } from '@/screens/dashboard-detail/components/ai-builder-modal/handlers/TextHandler';
import { ChartMessageHelper } from '@/screens/dashboard-detail/components/ai-builder-modal/helpers/ChartMessageHelper';

export class MessageFactoryBuilder {
  private handlers: MessageHandler[] = [];

  /**
   * Add a custom handler to the factory
   */
  addHandler(handler: MessageHandler): MessageFactoryBuilder {
    this.handlers.push(handler);
    return this;
  }

  /**
   * Add chart handler
   */
  addChartHandler(chartHelper: ChartMessageHelper): MessageFactoryBuilder {
    this.handlers.push(new ChartHandler(chartHelper));
    return this;
  }

  /**
   * Add error handler
   */
  addErrorHandler(): MessageFactoryBuilder {
    this.handlers.push(new ErrorHandler());
    return this;
  }

  /**
   * Add text handler (fallback)
   */
  addTextHandler(): MessageFactoryBuilder {
    this.handlers.push(new TextHandler());
    return this;
  }

  /**
   * Add all default handlers
   */
  addDefaultHandlers(chartHelper: ChartMessageHelper): MessageFactoryBuilder {
    return this
      .addChartHandler(chartHelper)
      .addErrorHandler()
      .addTextHandler();
  }

  /**
   * Build the MessageFactory with configured handlers
   */
  build(): MessageFactory {
    if (this.handlers.length === 0) {
      throw new Error('MessageFactoryBuilder: At least one handler must be added before building');
    }

    // Sort handlers by priority (lower number = higher priority)
    const sortedHandlers = [...this.handlers].sort((a, b) => a.getPriority() - b.getPriority());
    
    return new MessageFactory(sortedHandlers);
  }

  /**
   * Create a default factory with all standard handlers
   */
  static createDefault(chartHelper: ChartMessageHelper): MessageFactory {
    return new MessageFactoryBuilder()
      .addDefaultHandlers(chartHelper)
      .build();
  }

  /**
   * Create a minimal factory with only text handler
   */
  static createMinimal(): MessageFactory {
    return new MessageFactoryBuilder()
      .addTextHandler()
      .build();
  }

  /**
   * Create a factory without chart processing (for testing or specific use cases)
   */
  static createWithoutChart(): MessageFactory {
    return new MessageFactoryBuilder()
      .addErrorHandler()
      .addTextHandler()
      .build();
  }
}
