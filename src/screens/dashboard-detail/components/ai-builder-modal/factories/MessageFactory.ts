import { MessageHandler, MessageProcessingResponse } from '@/screens/dashboard-detail/components/ai-builder-modal/handlers/MessageHandler';
import { Log } from '@core/utils';

export class MessageFactory {
  constructor(private handlers: MessageHandler[]) {
    if (!handlers || handlers.length === 0) {
      throw new Error('MessageFactory: At least one handler must be provided');
    }
  }

  /**
   * Build and process message response from LLM
   */
  buildAndProcessMessage(responseText: string): MessageProcessingResponse {
    try {
      // Find the first handler that can handle this response
      for (const handler of this.handlers) {
        if (handler.canHandle(responseText)) {
          Log.debug('MessageFactory::buildAndProcessMessage::using handler::', handler.constructor.name);
          return handler.handle(responseText);
        }
      }

      // This should not happen if TextHandler is included as fallback
      throw new Error('No handler found for response text');
    } catch (ex) {
      Log.error('MessageFactory::buildAndProcessMessage::error::', ex);

      // Try to find an error handler as fallback
      const errorHandler = this.handlers.find(h => h.constructor.name === 'ErrorHandler');
      if (errorHandler) {
        return errorHandler.handle(`Failed to process message: ${ex.message || ex}`);
      }

      // Ultimate fallback - create a basic error response
      throw ex;
    }
  }

  /**
   * Process chart message and emit events
   */
  private processChartMessage(responseText: string): MessageProcessingResponse {
    try {
      const message = this.buildChartMessage(responseText);

      // Process chart configuration and emit events
      this.chartHelper.processChartMessage(responseText);

      return {
        message,
        result: MessageProcessingResult.CHART_PROCESSED
      };
    } catch (ex) {
      Log.error('MessageFactory::processChartMessage::error::', ex);
      return this.processErrorMessage(`Failed to process chart: ${ex.message || ex}`);
    }
  }

  /**
   * Process error message
   */
  private processErrorMessage(responseText: string): MessageProcessingResponse {
    const message = this.buildErrorMessage(responseText);

    return {
      message,
      result: MessageProcessingResult.ERROR_PROCESSED,
      error: responseText
    };
  }

  /**
   * Process regular text message
   */
  private processTextMessage(responseText: string): MessageProcessingResponse {
    const message = this.buildTextMessage(responseText);

    return {
      message,
      result: MessageProcessingResult.TEXT_PROCESSED
    };
  }

  /**
   * Build chart message
   */
  private buildChartMessage(responseText: string): ChatMessageData {
    return {
      role: OpenAiMessageRole.assistant,
      type: MessageType.chart,
      text: responseText
    };
  }

  /**
   * Build error message
   */
  private buildErrorMessage(responseText: string): ChatMessageData {
    return {
      role: OpenAiMessageRole.assistant,
      type: MessageType.error,
      text: responseText
    };
  }

  /**
   * Build text message
   */
  private buildTextMessage(responseText: string): ChatMessageData {
    return {
      role: OpenAiMessageRole.assistant,
      type: MessageType.text,
      text: responseText
    };
  }

  /**
   * Check if response contains error indicators
   */
  private hasErrorIndicators(responseText: string): boolean {
    const errorKeywords = ['error', 'failed', 'exception', 'invalid', 'cannot', 'unable to', 'not found', 'not supported'];

    const lowerText = responseText.toLowerCase();
    return errorKeywords.some(keyword => lowerText.includes(keyword));
  }
}
