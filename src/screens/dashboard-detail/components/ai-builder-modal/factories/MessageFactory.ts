import { ChatMessageData } from '@/screens/dashboard-detail/intefaces/chatbot/ChatMessageData';
import { OpenAiMessageRole } from '@core/chat-bot/domain/OpenAiMessageRole';
import { MessageType } from '@/screens/dashboard-detail/intefaces/chatbot/MessageType';
import { ChartMessageHelper } from '@/screens/dashboard-detail/components/ai-builder-modal/helpers/ChartMessageHelper';
import { Log } from '@core/utils';

export enum MessageProcessingResult {
  CHART_PROCESSED = 'chart_processed',
  ERROR_PROCESSED = 'error_processed',
  TEXT_PROCESSED = 'text_processed'
}

export interface MessageProcessingResponse {
  message: ChatMessageData;
  result: MessageProcessingResult;
  error?: string;
}

export class MessageFactory {
  constructor(private chartHelper: ChartMessageHelper) {}

  /**
   * Build and process message response from LLM
   */
  buildAndProcessMessage(responseText: string): MessageProcessingResponse {
    try {
      // Check if response contains chart data
      if (ChartMessageHelper.hasChartData(responseText)) {
        return this.processChartMessage(responseText);
      }

      // Check if response contains error indicators
      if (this.hasErrorIndicators(responseText)) {
        return this.processErrorMessage(responseText);
      }

      // Default to text message
      return this.processTextMessage(responseText);
    } catch (ex) {
      Log.error('MessageFactory::buildAndProcessMessage::error::', ex);
      return this.processErrorMessage(`Failed to process message: ${ex.message || ex}`);
    }
  }

  /**
   * Process chart message and emit events
   */
  private processChartMessage(responseText: string): MessageProcessingResponse {
    try {
      const message = this.buildChartMessage(responseText);

      // Process chart configuration and emit events
      this.chartHelper.processChartMessage(responseText);

      return {
        message,
        result: MessageProcessingResult.CHART_PROCESSED
      };
    } catch (ex) {
      Log.error('MessageFactory::processChartMessage::error::', ex);
      return this.processErrorMessage(`Failed to process chart: ${ex.message || ex}`);
    }
  }

  /**
   * Process error message
   */
  private processErrorMessage(responseText: string): MessageProcessingResponse {
    const message = this.buildErrorMessage(responseText);

    return {
      message,
      result: MessageProcessingResult.ERROR_PROCESSED,
      error: responseText
    };
  }

  /**
   * Process regular text message
   */
  private processTextMessage(responseText: string): MessageProcessingResponse {
    const message = this.buildTextMessage(responseText);

    return {
      message,
      result: MessageProcessingResult.TEXT_PROCESSED
    };
  }

  /**
   * Build chart message
   */
  private buildChartMessage(responseText: string): ChatMessageData {
    return {
      role: OpenAiMessageRole.assistant,
      type: MessageType.chart,
      text: responseText
    };
  }

  /**
   * Build error message
   */
  private buildErrorMessage(responseText: string): ChatMessageData {
    return {
      role: OpenAiMessageRole.assistant,
      type: MessageType.error,
      text: responseText
    };
  }

  /**
   * Build text message
   */
  private buildTextMessage(responseText: string): ChatMessageData {
    return {
      role: OpenAiMessageRole.assistant,
      type: MessageType.text,
      text: responseText
    };
  }

  /**
   * Check if response contains error indicators
   */
  private hasErrorIndicators(responseText: string): boolean {
    const errorKeywords = ['error', 'failed', 'exception', 'invalid', 'cannot', 'unable to', 'not found', 'not supported'];

    const lowerText = responseText.toLowerCase();
    return errorKeywords.some(keyword => lowerText.includes(keyword));
  }
}
