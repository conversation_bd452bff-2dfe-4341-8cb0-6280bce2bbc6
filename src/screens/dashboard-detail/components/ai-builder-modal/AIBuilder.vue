<script lang="ts">
import { Component, Prop, Ref } from 'vue-property-decorator';
import SplitPanelMixin from '@/shared/components/layout-wrapper/SplitPanelMixin';
import AIChartPreviewPanel from '@/screens/dashboard-detail/components/ai-builder-modal/panel/preview-panel/AIChartPreviewPanel.vue';
import AIChatPanel from '@/screens/dashboard-detail/components/ai-builder-modal/panel/AIChatPanel.vue';
import { AIBuilderController } from '@/screens/dashboard-detail/components/ai-builder-modal/AIBuilderController';
import EventBus from '@/screens/dashboard-detail/components/chatbot/helpers/EventBus';
import { AIChatModalEvents } from '@/screens/dashboard-detail/components/ai-builder-modal/AIChatModalEvents';

@Component({
  components: { AIChatPanel, AIChartPreviewPanel }
})
export default class AIBuilder extends SplitPanelMixin {
  @Prop({ required: true })
  protected controller!: AIBuilderController;

  protected get panelSize() {
    return this.getPanelSizeHorizontal();
  }

  private get collapsedSidebarSize() {
    return [0, 100];
  }

  protected resizeChart() {
    //
  }

  @Ref()
  private splitPanel!: SplitPanelMixin;

  mounted() {
    EventBus.$on(AIChatModalEvents.toggleSidebar, this.toggleSidebar);
  }

  beforeDestroy() {
    EventBus.$off(AIChatModalEvents.toggleSidebar, this.toggleSidebar);
  }

  private toggleSidebar() {
    //@ts-ignore
    const splitPanel = this.splitPanel?.instance;
    if (splitPanel && splitPanel.getSizes && splitPanel.setSizes) {
      this.addAnimationClass();
      splitPanel.setSizes(this.getNextSize(splitPanel));
      this.removeAnimationClassAfterDelay();
    }
  }

  private getNextSize(panelRef: { getSizes: () => number[] }) {
    const sizes = panelRef.getSizes();
    const isCollapsing = sizes[0] === this.collapsedSidebarSize[0];
    //Open
    if (isCollapsing) {
      return this.panelSize;
    }
    //Collapse
    return this.collapsedSidebarSize;
  }

  private addAnimationClass() {
    const container = document.getElementById('ai-builder-container');
    if (container) {
      container.classList.add('sidebar-toggle-animation');
    }
  }

  private removeAnimationClassAfterDelay() {
    setTimeout(() => {
      const container = document.getElementById('ai-builder-container');
      if (container) {
        container.classList.remove('sidebar-toggle-animation');
      }
    }, 300);
  }
}
</script>

<template>
  <Split :gutterSize="1" ref="splitPanel" class="d-flex" id="ai-builder-container" @onDragEnd="resizeChart">
    <SplitArea :size="panelSize[0]" :minSize="0">
      <AIChatPanel :controller="controller" />
    </SplitArea>

    <SplitArea :size="panelSize[1]" :minSize="0">
      <AIChartPreviewPanel :controller="controller" />
    </SplitArea>
  </Split>
</template>

<style lang="scss">
#ai-builder-container {
  &:has(.split:first-of-type[style*='width: calc(0% - 0.5px)']) {
    .gutter-horizontal {
      background-color: transparent;
    }
  }

  .gutter-horizontal:before {
    display: none;
  }

  &.sidebar-toggle-animation {
    .split {
      transition: width 0.3s ease-in-out;
    }

    .gutter-horizontal {
      transition: opacity 0.3s ease-in-out;
    }
  }
}
</style>
