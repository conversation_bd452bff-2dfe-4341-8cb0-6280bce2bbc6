import { ChartInfo, ChartOption, Id, QuerySetting, WidgetCommonData, WidgetExtraData } from '@core/common/domain';
import { cloneDeep } from 'lodash';
import { ConditionData, ConfigType, FunctionData } from '@/shared';
import { _ConfigBuilderStore } from '@/screens/chart-builder/config-builder/ConfigBuilderStore';

export class ChartInfoGenerator {
  private static readonly PREVIEW_CHART_ID = -1;

  public generate(querySetting: QuerySetting): ChartInfo {
    const commonSetting: WidgetCommonData = this.getWidgetCommonData(querySetting.getChartOption());
    const cloneQuerySetting = cloneDeep(querySetting);
    return new ChartInfo(commonSetting, cloneQuerySetting);
  }

  private getWidgetCommonData(chartOption: ChartOption | null | undefined): WidgetCommonData {
    return {
      id: ChartInfoGenerator.PREVIEW_CHART_ID,
      name: chartOption?.getTitle() ?? '',
      description: chartOption?.getSubtitle() ?? '',
      extraData: this.getExtraData(),
      backgroundColor: chartOption?.getBackgroundColor() || '#0000001A',
      textColor: chartOption?.getTextColor() || '#fff'
    };
  }

  private getExtraData(): WidgetExtraData {
    const configs: Record<ConfigType, FunctionData[]> = Object.fromEntries(_ConfigBuilderStore.configsAsMap) as Record<ConfigType, FunctionData[]>;
    const filters: Record<Id, ConditionData[]> = Object.fromEntries(_ConfigBuilderStore.filterAsMap);
    return {
      configs: configs,
      filters: filters,
      currentChartType: _ConfigBuilderStore.chartType
    } as WidgetExtraData;
  }
}
