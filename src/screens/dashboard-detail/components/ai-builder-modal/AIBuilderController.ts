import { ChatMessageData } from '@/screens/dashboard-detail/intefaces/chatbot/ChatMessageData';
import { ChartInfo, TableSchema } from '@core/common/domain';
import { MessageAction } from '@/screens/dashboard-detail/intefaces/chatbot/MessageAction';
import { Log } from '@core/utils';
import { OpenAiMessageRole } from '@core/chat-bot/domain/OpenAiMessageRole';
import { MessageType } from '@/screens/dashboard-detail/intefaces/chatbot/MessageType';
import { ChartGenerationMetadata, LLMController, RocketBiAiController } from '@/screens/dashboard-detail/intefaces/chatbot/ChatbotController';
import { _BuilderTableSchemaStore } from '@/store/modules/data-builder/BuilderTableSchemaStore';
import { isString } from 'lodash';

export class AIBuilderController {
  public conversations: ChatMessageData[] = [];
  public typing = false;
  public chartInfo?: ChartInfo | null;
  private llm: LLMController;

  constructor(conversations: ChatMessageData[], chartInfo?: ChartInfo | null) {
    this.conversations = conversations;
    this.chartInfo = chartInfo;
    this.llm = new RocketBiAiController();
  }

  static empty(): AIBuilderController {
    return new AIBuilderController([]);
  }

  setChartInfo(chartInfo: ChartInfo | null) {
    this.chartInfo = chartInfo;
  }

  private addMessage(msg: { text: string }) {
    const userMessage = this.buildUserMessage(msg.text);
    this.conversations.push(userMessage);
  }

  private buildUserMessage(msg: string): ChatMessageData {
    return {
      role: OpenAiMessageRole.user,
      type: MessageType.text,
      text: msg
    };
  }

  private buildChartMessage(res: string): ChatMessageData {
    return {
      role: OpenAiMessageRole.assistant,
      type: MessageType.chart,
      text: res
    };
  }

  async sendMessage(msg: MessageAction) {
    Log.debug('sendMessage::', msg);
    this.addMessage(msg);
    return this.completions();
  }

  private async completions() {
    try {
      this.typing = true;
      const options: ChartGenerationMetadata = {
        table: _BuilderTableSchemaStore.tableSchemas.filter(table => table.isExpanded && table.data).map(table => table.data as TableSchema)
      };
      const message: string = await this.llm.generateChart(this.conversations, options);
      this.conversations.push(this.buildChartMessage(message));
    } catch (ex) {
      Log.error('AIBuilderController::completions::error::', ex);
      this.handleError(ex);
    } finally {
      this.typing = false;
    }
  }

  public handleError(ex: any) {
    let errorMsg: ChatMessageData | null = null;
    errorMsg = {
      role: OpenAiMessageRole.assistant,
      type: MessageType.error,
      text: isString(ex) ? ex : ex.message
    };
    errorMsg ? this.conversations.push(errorMsg) : void 0;
  }
}
