import { ChatMessageData } from '@/screens/dashboard-detail/intefaces/chatbot/ChatMessageData';
import { ChartInfo, TableSchema } from '@core/common/domain';
import { MessageAction } from '@/screens/dashboard-detail/intefaces/chatbot/MessageAction';
import { Log } from '@core/utils';
import { OpenAiMessageRole } from '@core/chat-bot/domain/OpenAiMessageRole';
import { MessageType } from '@/screens/dashboard-detail/intefaces/chatbot/MessageType';
import { ChartGenerationMetadata, LLMController, RocketBiAiController } from '@/screens/dashboard-detail/intefaces/chatbot/ChatbotController';
import { _BuilderTableSchemaStore } from '@/store/modules/data-builder/BuilderTableSchemaStore';
import { isString } from 'lodash';
import { ChartMessageHelper } from '@/screens/dashboard-detail/components/ai-builder-modal/helpers/ChartMessageHelper';
import { MessageFactory } from '@/screens/dashboard-detail/components/ai-builder-modal/factories/MessageFactory';
import { DraggableSettingResolver } from '@/shared/resolver';
import { Inject as IOCInject } from 'typescript-ioc';

export class AIBuilderController {
  public conversations: ChatMessageData[] = [];
  public typing = false;
  public chartInfo?: ChartInfo | null;
  private llm: LLMController;
  private chartHelper: ChartMessageHelper | null = null;
  private messageFactory: MessageFactory | null = null;

  @IOCInject
  private factory!: DraggableSettingResolver;

  constructor(conversations: ChatMessageData[], chartInfo?: ChartInfo | null) {
    this.conversations = conversations;
    this.chartInfo = chartInfo;
    this.llm = new RocketBiAiController();
  }

  /**
   * Initialize helpers lazily after dependency injection
   */
  private initializeHelpers(): void {
    if (!this.chartHelper) {
      this.chartHelper = new ChartMessageHelper(this.factory);
    }
    if (!this.messageFactory) {
      this.messageFactory = new MessageFactory(this.chartHelper);
    }
  }

  /**
   * Get message factory instance
   */
  private getMessageFactory(): MessageFactory {
    this.initializeHelpers();
    return this.messageFactory!;
  }

  static empty(): AIBuilderController {
    return new AIBuilderController([]);
  }

  setChartInfo(chartInfo: ChartInfo | null) {
    this.chartInfo = chartInfo;
  }

  private addMessage(msg: { text: string }) {
    const userMessage = this.buildUserMessage(msg.text);
    this.conversations.push(userMessage);
  }

  private buildUserMessage(msg: string): ChatMessageData {
    return {
      role: OpenAiMessageRole.user,
      type: MessageType.text,
      text: msg
    };
  }



  async sendMessage(msg: MessageAction) {
    Log.debug('sendMessage::', msg);
    this.addMessage(msg);
    return this.completions();
  }

  private async completions() {
    try {
      this.typing = true;
      const options: ChartGenerationMetadata = {
        table: _BuilderTableSchemaStore.tableSchemas.filter(table => table.isExpanded && table.data).map(table => table.data as TableSchema)
      };

      const responseText: string = await this.llm.generateChart(this.conversations, options);

      // Use factory to build and process message
      const messageFactory = this.getMessageFactory();
      const processingResponse = messageFactory.buildAndProcessMessage(responseText);
      this.conversations.push(processingResponse.message);

      // Log processing result for debugging
      Log.debug('AIBuilderController::completions::processing result::', processingResponse.result);

      // Handle any processing errors
      if (processingResponse.error) {
        Log.warn('AIBuilderController::completions::processing warning::', processingResponse.error);
      }

    } catch (ex) {
      Log.error('AIBuilderController::completions::error::', ex);
      this.handleError(ex);
    } finally {
      this.typing = false;
    }
  }

  public handleError(ex: any) {
    let errorMsg: ChatMessageData | null = null;
    errorMsg = {
      role: OpenAiMessageRole.assistant,
      type: MessageType.error,
      text: isString(ex) ? ex : ex.message
    };
    errorMsg ? this.conversations.push(errorMsg) : void 0;
  }
}
