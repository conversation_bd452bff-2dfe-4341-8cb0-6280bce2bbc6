<script lang="ts">
import { Vue, Component, Provide } from 'vue-property-decorator';
import { AIBuilderController } from '@/screens/dashboard-detail/components/ai-builder-modal/AIBuilderController';
import AIBuilder from '@/screens/dashboard-detail/components/ai-builder-modal/AIBuilder.vue';
import { ChartInfo, QuerySetting } from '@core/common/domain';
import EventBus from '@/screens/dashboard-detail/components/chatbot/helpers/EventBus';
import { AIChatModalEvents } from '@/screens/dashboard-detail/components/ai-builder-modal/AIChatModalEvents';
import { MessageAction } from '@/screens/dashboard-detail/intefaces/chatbot/MessageAction';
import { _ConfigBuilderStore } from '@/screens/chart-builder/config-builder/ConfigBuilderStore';
import { Log } from '@core/utils';
import { ChartInfoGenerator } from '@/screens/dashboard-detail/components/ai-builder-modal/ChartInfoGenerator';

type Callbacks = {
  onSwitchToManualSetting: (chartInfo?: ChartInfo) => void;
};
@Component({
  components: { AIBuilder }
})
export default class AIBuilderModal extends Vue {
  protected controller: AIBuilderController | null = null;
  protected callbacks: Callbacks | null = null;
  protected isShow = false;

  mounted() {
    EventBus.$on(AIChatModalEvents.sendMessage, this.handleSendMessage);
    EventBus.$on(AIChatModalEvents.onConfigChanged, this.handleConfigChanged);
    EventBus.$on(AIChatModalEvents.switchToManualSetting, this.handleSwitchToManualSetting);
  }

  beforeDestroy() {
    EventBus.$off(AIChatModalEvents.sendMessage, this.handleSendMessage);
    EventBus.$off(AIChatModalEvents.onConfigChanged, this.handleConfigChanged);
    EventBus.$off(AIChatModalEvents.switchToManualSetting, this.handleSwitchToManualSetting);
  }

  private handleSendMessage(msg: MessageAction) {
    if (!this.controller) {
      return;
    }

    this.controller.sendMessage(msg);
  }

  public show(controller: AIBuilderController, callbacks?: Callbacks) {
    this.controller = controller;
    this.callbacks = callbacks ?? null;
    this.isShow = true;
  }

  protected onModalHidden() {
    this.controller = null;
    this.callbacks = null;
  }

  protected onModalShown() {
    // this.isShow = true;
  }

  public handleConfigChanged() {
    try {
      const querySetting: QuerySetting | null = this.getQuerySetting();
      if (!querySetting || !this.controller) {
        return;
      }

      const chartInfo = new ChartInfoGenerator().generate(querySetting);
      this.controller?.setChartInfo(chartInfo);
    } catch (ex) {
      Log.error('AIBuilderModal::handleConfigChanged::error::', ex);
      if (this.controller) {
        this.controller.handleError(ex);
      }
    }
  }

  public getQuerySetting(): QuerySetting | null {
    const hasQuerySetting: boolean = _ConfigBuilderStore.hasQuerySetting();
    if (hasQuerySetting) {
      return _ConfigBuilderStore.getQuerySetting();
    } else {
      return null;
    }
  }

  public handleSwitchToManualSetting() {
    if (this.callbacks?.onSwitchToManualSetting && this.controller?.chartInfo) {
      this.callbacks.onSwitchToManualSetting(this.controller.chartInfo);
    }
    this.isShow = false;
  }
}
</script>

<template>
  <BModal
    id="ai-builder-modal"
    v-model="isShow"
    :cancel-disabled="false"
    hide-footer
    hide-header
    centered
    class="rounded"
    size="lg"
    static
    @hidden="onModalHidden"
    @shown="onModalShown"
  >
    <AIBuilder v-if="controller" :controller="controller" />
  </BModal>
</template>

<style lang="scss">
#ai-builder-modal {
  .modal-dialog {
    max-width: 90vw;
  }

  .modal-content {
    height: 80vh;
    border-radius: 24px !important;
  }

  .modal-body {
    padding: unset;
  }
}
</style>
