import { Message<PERSON><PERSON><PERSON>, MessageProcessingResponse, MessageProcessingResult } from './MessageHandler';
import { ChatMessageData } from '@/screens/dashboard-detail/intefaces/chatbot/ChatMessageData';
import { OpenAiMessageRole } from '@core/chat-bot/domain/OpenAiMessageRole';
import { MessageType } from '@/screens/dashboard-detail/intefaces/chatbot/MessageType';

export class ErrorHandler implements MessageHandler {
  private readonly errorKeywords = [
    'error',
    'failed',
    'exception',
    'invalid',
    'cannot',
    'unable to',
    'not found',
    'not supported',
    'syntax error',
    'permission denied',
    'access denied',
    'timeout',
    'connection failed'
  ];

  canHandle(responseText: string): boolean {
    const lowerText = responseText.toLowerCase();
    return this.errorKeywords.some(keyword => lowerText.includes(keyword));
  }

  handle(responseText: string): MessageProcessingResponse {
    const message = this.buildErrorMessage(responseText);
    
    return {
      message,
      result: MessageProcessingResult.ERROR_PROCESSED,
      error: responseText
    };
  }

  getPriority(): number {
    return 2; // Second priority
  }

  private buildErrorMessage(responseText: string): ChatMessageData {
    return {
      role: OpenAiMessageRole.assistant,
      type: MessageType.error,
      text: responseText
    };
  }
}
