import { Message<PERSON><PERSON><PERSON>, MessageProcessingResponse, MessageProcessingResult } from './MessageHandler';
import { ChatMessageData } from '@/screens/dashboard-detail/intefaces/chatbot/ChatMessageData';
import { OpenAiMessageRole } from '@core/chat-bot/domain/OpenAiMessageRole';
import { MessageType } from '@/screens/dashboard-detail/intefaces/chatbot/MessageType';
import { ChartMessageHelper } from '@/screens/dashboard-detail/components/ai-builder-modal/helpers/ChartMessageHelper';
import { Log } from '@core/utils';

export class ChartHandler implements MessageHandler {
  constructor(private chartHelper: ChartMessageHelper) {}

  canHandle(responseText: string): boolean {
    return ChartMessageHelper.hasChartData(responseText);
  }

  handle(responseText: string): MessageProcessingResponse {
    try {
      const message = this.buildChartMessage(responseText);
      
      // Process chart configuration and emit events
      this.chartHelper.processChartMessage(responseText);
      
      return {
        message,
        result: MessageProcessingResult.CHART_PROCESSED
      };
    } catch (ex) {
      Log.error('ChartHandler::handle::error::', ex);
      return {
        message: this.buildErrorMessage(`Failed to process chart: ${ex.message || ex}`),
        result: MessageProcessingResult.ERROR_PROCESSED,
        error: `Failed to process chart: ${ex.message || ex}`
      };
    }
  }

  getPriority(): number {
    return 1; // Highest priority
  }

  private buildChartMessage(responseText: string): ChatMessageData {
    return {
      role: OpenAiMessageRole.assistant,
      type: MessageType.chart,
      text: responseText
    };
  }

  private buildErrorMessage(responseText: string): ChatMessageData {
    return {
      role: OpenAiMessageRole.assistant,
      type: MessageType.error,
      text: responseText
    };
  }
}
