import { ChatMessageData } from '@/screens/dashboard-detail/intefaces/chatbot/ChatMessageData';

export enum MessageProcessingResult {
  CHART_PROCESSED = 'chart_processed',
  ERROR_PROCESSED = 'error_processed',
  TEXT_PROCESSED = 'text_processed'
}

export interface MessageProcessingResponse {
  message: ChatMessageData;
  result: MessageProcessingResult;
  error?: string;
}

export interface MessageHandler {
  /**
   * Check if this handler can process the given response text
   */
  canHandle(responseText: string): boolean;

  /**
   * Process the response text and return the result
   */
  handle(responseText: string): MessageProcessingResponse;

  /**
   * Get the priority of this handler (lower number = higher priority)
   */
  getPriority(): number;
}
