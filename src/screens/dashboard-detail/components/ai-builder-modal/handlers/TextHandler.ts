import { Message<PERSON><PERSON><PERSON>, MessageProcessingResponse, MessageProcessingResult } from './MessageHandler';
import { ChatMessageData } from '@/screens/dashboard-detail/intefaces/chatbot/ChatMessageData';
import { OpenAiMessageRole } from '@core/chat-bot/domain/OpenAiMessageRole';
import { MessageType } from '@/screens/dashboard-detail/intefaces/chatbot/MessageType';

export class TextHandler implements MessageHandler {
  canHandle(responseText: string): boolean {
    // TextHandler is the fallback handler, so it can handle any text
    return true;
  }

  handle(responseText: string): MessageProcessingResponse {
    const message = this.buildTextMessage(responseText);
    
    return {
      message,
      result: MessageProcessingResult.TEXT_PROCESSED
    };
  }

  getPriority(): number {
    return 999; // Lowest priority (fallback)
  }

  private buildTextMessage(responseText: string): ChatMessageData {
    return {
      role: OpenAiMessageRole.assistant,
      type: MessageType.text,
      text: responseText
    };
  }
}
