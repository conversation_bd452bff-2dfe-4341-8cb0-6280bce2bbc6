<template>
  <div class="di-msg-bubble-component di-msg-bubble-component--single-text">
    <div class="di-msg-bubble-component__text text-left" v-html="textDisplay"></div>
  </div>
</template>
<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator';
import { ChatMessageData } from '@/screens/dashboard-detail/intefaces/chatbot/ChatMessageData';
import { Inject as IOCInject } from 'typescript-ioc';
import { ChartMessageHelper } from '@/screens/dashboard-detail/components/ai-builder-modal/helpers/ChartMessageHelper';
import { DraggableSettingResolver } from '@/shared/resolver';

@Component
export default class ChartMessage extends Vue {
  @Prop() mainData!: ChatMessageData;

  @IOCInject
  private factory!: DraggableSettingResolver;

  private chartHelper!: ChartMessageHelper;

  created() {
    this.chartHelper = new ChartMessageHelper(this.factory);
  }

  get textDisplay() {
    return this.chartHelper.parseResponse(this.mainData.text).text;
  }


}
</script>
